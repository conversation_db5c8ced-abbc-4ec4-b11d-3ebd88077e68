"""
Database model for shared queries.
"""
import uuid
import datetime
from typing import Dict, Any
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, BigInteger, Text, Boolean, Index, JSON
from sqlalchemy.sql import func
from sqlalchemy.types import TypeDecorator
import json

from models.base import Base

# Custom JSON type for databases that don't support JSON natively
class JSONType(TypeDecorator):
    """
    Custom JSON type for SQLAlchemy that serializes/deserializes JSON data.
    """
    impl = Text

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return None

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return None

class SharedQuery(Base):
    """
    SharedQuery model for managing public query shares.
    """
    __tablename__ = "shared_queries"
    __table_args__ = (
        Index('idx_share_id', 'share_id'),
        Index('idx_user_id', 'user_id'),
        Index('idx_active_expires', 'is_active', 'expires_at'),
        {'schema': None}  # Schema will be set dynamically
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    share_id = Column(String(36), unique=True, nullable=False)  # UUID
    user_query_ids = Column(JSON().with_variant(JSONType, "mysql"), nullable=False)  # Array of query IDs
    user_id = Column(String(100), nullable=False)  # auth0_sub of owner
    shared_by = Column(String(100), nullable=False)  # who shared it
    title = Column(String(255), nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    expires_at = Column(DateTime, nullable=True)
    view_count = Column(Integer, nullable=False, default=0)
    last_viewed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    @classmethod
    def set_schema(cls, schema_name):
        """Set the schema for this model."""
        cls.__table__.schema = schema_name
    
    @classmethod
    def generate_share_id(cls) -> str:
        """Generate a new UUID for sharing."""
        return str(uuid.uuid4())
    
    def is_expired(self) -> bool:
        """Check if the share has expired."""
        if self.expires_at is None:
            return False
        return datetime.datetime.now() > self.expires_at
    
    def is_accessible(self) -> bool:
        """Check if the share is accessible (active and not expired)."""
        return self.is_active and not self.is_expired()
    
    def increment_view_count(self):
        """Increment view count and update last viewed time."""
        self.view_count += 1
        self.last_viewed_at = datetime.datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary."""
        return {
            "id": self.id,
            "share_id": self.share_id,
            "query_ids": self.query_ids,
            "user_id": self.user_id,
            "shared_by": self.shared_by,
            "title": self.title,
            "description": self.description,
            "is_active": self.is_active,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "view_count": self.view_count,
            "last_viewed_at": self.last_viewed_at.isoformat() if self.last_viewed_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def to_public_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for public consumption (no sensitive data)."""
        return {
            "share_id": self.share_id,
            "title": self.title,
            "description": self.description,
            "view_count": self.view_count,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
